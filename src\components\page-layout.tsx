import { BreadcrumbItemType } from "@/types/breadcrumb";
import { AppSidebarHeader } from "./app-sidebar-header";

interface PageLayoutProps {
  breadcrumbs: BreadcrumbItemType[];
  children: React.ReactNode;
}

export function PageLayout({ breadcrumbs, children }: PageLayoutProps) {
  return (
    <>
      <AppSidebarHeader breadcrumbs={breadcrumbs} />
      <div className="pt-0 p-4">{children}</div>
    </>
  );
}
