import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
// 6 giờ (ms)
const CACHE_TTL = 6 * 60 * 60 * 1000;
// Tạo cache lưu token đã validate
const tokenCache = new Map<string, { valid: boolean; exp: number }>();

async function validateToken(token: string): Promise<boolean> {
  const now = Date.now();
  const cached = tokenCache.get(token);

  if (cached && cached.exp > now) {
    return cached.valid;
  }

  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/auth/verify`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      cache: "no-store",
    });

    const valid = res.ok;

    // Lưu cache 6h
    tokenCache.set(token, { valid, exp: now + CACHE_TTL });

    return valid;
  } catch (e) {
    console.error("Token validation error:", e);
    return false;
  }
}
export async function middleware(request: NextRequest) {
  const token = request.cookies.get("auth_access_token")?.value;

  const publicRoutes = ["/login", "/register", "/forgot-password"];
  const { pathname } = request.nextUrl;

  if (publicRoutes.includes(pathname)) {
    return NextResponse.next();
  }

  if (token) {
    const loginUrl = new URL("/login", request.url);
    loginUrl.searchParams.set("redirect", pathname);
    return NextResponse.redirect(loginUrl);
  }

  // Check token hợp lệ
  // const isValid = await validateToken(token);
  // if (!isValid) {
  //   const loginUrl = new URL("/login", request.url);
  //   loginUrl.searchParams.set("redirect", pathname);
  //   return NextResponse.redirect(loginUrl);
  // }

  return NextResponse.next();
}

export const config = {
  matcher: ["/dashboard/:path*"],
};
