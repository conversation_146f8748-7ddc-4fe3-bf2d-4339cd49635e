stages:
  - version
  - build

variables:
  DOCKER_TLS_CERTDIR: ""
  IMAGE_VERSION: "$CI_REGISTRY_IMAGE:${CI_COMMIT_TAG:-0.0.$CI_PIPELINE_ID}"
  IMAGE_LATEST: "$CI_REGISTRY_IMAGE:latest"


create_version:
  stage: version
  image: registryrb.litedoozy.com:5050/base/image-alpine:3.19
  tags:
    - lite-runner
  before_script:
    - apk add --no-cache git
    - git config --global user.email "$GITLAB_USER_EMAIL"
    - git config --global user.name "$GITLAB_USER_NAME"
    - git remote set-url origin "https://gitlab-ci-token:${REGISTRY_PASSWORD}@${CI_SERVER_HOST}/${CI_PROJECT_PATH}.git"
    - git fetch --tags
  script:
    - |
      # Get the latest tag that matches the pattern
      LATEST_TAG=$(git tag -l "0.0.*" | sort -V | tail -n1)
      if [ -z "$LATEST_TAG" ]; then
        NEW_VERSION="0.0.1"
      else
        # Extract the patch number and increment
        PATCH=$(echo $LATEST_TAG | cut -d'.' -f3)
        NEW_PATCH=$((PATCH + 1))
        NEW_VERSION="0.0.$NEW_PATCH"
      fi
      
      # Create and push the new tag
      git tag $NEW_VERSION
      git push origin $NEW_VERSION
      
      echo "Generated version: $NEW_VERSION"
  only:
    - prd

build:
  stage: build
  only:
    - prd
  needs: ["create_version"]
  tags:
    - lite-runner
  image: registryrb.litedoozy.com:5050/base/image-docker:20.10.7
  services:
    - name: registryrb.litedoozy.com:5050/base/image-docker:20.10.7-dind
      alias: docker
  retry:
    max: 2
    when:
      - runner_system_failure
      - stuck_or_timeout_failure
  dependencies:
    - create_version
  before_script:
    - apk add --no-cache git
    - git fetch --tags
    - |
      # Get the latest tag (same logic)
      LATEST_TAG=$(git tag -l "0.0.*" | sort -V | tail -n1)
      export IMAGE_VERSION="$CI_REGISTRY_IMAGE:$LATEST_TAG"
      echo "Using IMAGE_VERSION: $IMAGE_VERSION"
  script:
    - echo "Building version:$IMAGE_VERSION"
    - docker build -t "$IMAGE_VERSION" .
    - docker tag "$IMAGE_VERSION" "$IMAGE_LATEST"
    - docker push "$IMAGE_VERSION"
    - docker push "$IMAGE_LATEST"