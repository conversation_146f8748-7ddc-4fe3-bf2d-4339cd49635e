import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";

const categories = [
  "All",
  "BNB Chain",
  "Solana",
  "RWA",
  "Meme",
  "Payments",
  "AI",
  "Layer 1 / Layer 2",
  "Metaverse",
  "Seed",
  "Launchpool",
  "Megadrop",
  "Gaming",
];

const LIST_TAB_CATEGORY = categories.map((category: string) => ({
  value: category,
  label: category,
}));

export const TabCategory = () => {
  return (
    <Tabs defaultValue="account" className="w-full">
      <TabsList className="justify-start overflow-x-auto scrollbar-hide">
        <TabsTrigger value="account" className="whitespace-nowrap">
          Account
        </TabsTrigger>
        <TabsTrigger value="documents" className="whitespace-nowrap">
          Documents
        </TabsTrigger>
        <TabsTrigger value="settings" className="whitespace-nowrap">
          Settings
        </TabsTrigger>
      </TabsList>
      <div>
        <TabsContent value="account">
          <div>Make changes to your account.</div>
        </TabsContent>

        <TabsContent value="documents">
          <div>Access and update your documents.</div>
        </TabsContent>

        <TabsContent value="settings">
          <div>Edit your profile or update contact information.</div>
        </TabsContent>
      </div>
    </Tabs>

    // <div className="border-b border-border">
    //   <Tabs defaultValue="All">
    //     <TabsList className="px-6 py-3 space-x-6 overflow-x-auto">
    //       {LIST_TAB_CATEGORY.map((item) => (
    //         <TabsTrigger
    //           key={item.value}
    //           value={item.value}
    //           className={`text-sm whitespace-nowrap transition-colors `}
    //         >
    //           {item.label}
    //         </TabsTrigger>
    //       ))}
    //     </TabsList>
    //     {categories.map((category: string) => (
    //       <TabsContent key={category} value={category}>
    //         {category}
    //       </TabsContent>
    //     ))}
    //   </Tabs>
    // </div>
  );
};

{
  /* <div className="border-b border-border">
          <div className="flex items-center px-6 py-3 space-x-6 overflow-x-auto">
            {categories.map((category: string) => (
              <button
                key={category}
                onClick={() => setActiveCategory(category)}
                className={`text-sm whitespace-nowrap transition-colors ${
                  activeCategory === category
                    ? "text-primary bg-primary/10 px-3 py-1.5 rounded"
                    : "text-muted-foreground hover:text-foreground"
                }`}
              >
                {category}
                {category === "Solana" && (
                  <span className="ml-1 bg-emerald-500 text-white text-xs px-1 py-0.5 rounded">
                    New
                  </span>
                )}
                {(category === "Launchpool" || category === "Megadrop") && (
                  <span className="ml-1 bg-emerald-500 text-white text-xs px-1 py-0.5 rounded">
                    New
                  </span>
                )}
              </button>
            ))}
          </div> */
}
