"use client";

import { ChevronRight, type LucideIcon } from "lucide-react";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar";
import { usePathname, useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";

// Enhanced interface for navigation items
interface NavSubItem {
  title: string;
  url: string;
  icon?: LucideIcon;
  badge?: string | number;
}

interface NavItem {
  title: string;
  url?: string;
  icon?: LucideIcon;
  isActive?: boolean;
  items?: NavSubItem[];
  badge?: string | number;
  disabled?: boolean;
}

interface NavMainProps {
  items: NavItem[];
  groupLabel?: string;
}

export function NavMain({ items, groupLabel = "Platform" }: NavMainProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [openItems, setOpenItems] = useState<string[]>([]);

  // Helper function to check if a path is active
  const isPathActive = (url: string): boolean => {
    if (url === pathname) return true;
    return pathname.startsWith(url + "/");
  };

  // Check if any sub-item is active
  const hasActiveSubItem = (item: NavItem): boolean => {
    if (!item.items) return false;
    return item.items.some((subItem) => isPathActive(subItem.url));
  };

  // Check if parent should be open based on active sub-items
  const shouldParentBeOpen = (item: NavItem): boolean => {
    if (item.isActive) return true;
    return hasActiveSubItem(item);
  };

  // Initialize open items based on active state
  useEffect(() => {
    const initialOpenItems: string[] = [];
    items.forEach((item) => {
      if (shouldParentBeOpen(item)) {
        initialOpenItems.push(item.title);
      }
    });
    setOpenItems(initialOpenItems);
  }, [pathname, items]);

  // Handle navigation
  const handleNavigation = (url: string, event: React.MouseEvent) => {
    event.preventDefault();
    router.push(url);
  };

  // Toggle collapsible item
  const toggleItem = (itemTitle: string) => {
    setOpenItems((prev) => {
      if (prev.includes(itemTitle)) {
        return prev.filter((item) => item !== itemTitle);
      } else {
        return [...prev, itemTitle];
      }
    });
  };

  return (
    <SidebarGroup>
      <SidebarGroupLabel className="text-xs font-medium text-muted-foreground px-2 mb-2">
        {groupLabel}
      </SidebarGroupLabel>
      <SidebarMenu className="space-y-1">
        {items.map((item) => {
          const hasSubItems = !!item.items?.length;
          const isOpen = openItems.includes(item.title);
          const hasActiveSub = hasActiveSubItem(item);

          // Logic: Khi đóng thì parent active, khi mở thì chỉ sub-item active
          const shouldParentBeActive =
            !isOpen && ((item.url && isPathActive(item.url)) || hasActiveSub);

          return (
            <Collapsible
              key={item.title}
              open={isOpen}
              onOpenChange={() => hasSubItems && toggleItem(item.title)}
              className="group/collapsible"
            >
              <SidebarMenuItem>
                <CollapsibleTrigger asChild>
                  <SidebarMenuButton
                    className={cn(
                      "group w-full h-9 px-3 rounded-lg transition-all duration-200 hover:bg-accent/50",
                      "focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
                      shouldParentBeActive && [
                        "bg-gradient-to-r from-primary/10 to-primary/5",
                        "border-r-2 border-primary",
                        "text-primary font-medium",
                        "shadow-sm",
                      ],
                      item.disabled &&
                        "opacity-50 cursor-not-allowed hover:bg-transparent",
                      !shouldParentBeActive &&
                        "text-muted-foreground hover:text-foreground"
                    )}
                    tooltip={item.title}
                    onClick={(event) => {
                      if (item.disabled) return;

                      if (item.url && !hasSubItems) {
                        handleNavigation(item.url, event);
                      } else if (hasSubItems) {
                        event.preventDefault();
                        toggleItem(item.title);
                      }
                    }}
                    disabled={item.disabled}
                  >
                    <div className="flex items-center gap-3 flex-1">
                      {item.icon && (
                        <item.icon
                          className={cn(
                            "h-4 w-4 transition-colors flex-shrink-0",
                            shouldParentBeActive
                              ? "text-primary"
                              : "text-muted-foreground group-hover:text-foreground"
                          )}
                        />
                      )}
                      <span className="truncate font-medium">{item.title}</span>
                    </div>

                    <div className="flex items-center gap-2">
                      {/* Badge display */}
                      {item.badge && (
                        <span
                          className={cn(
                            "flex h-5 min-w-5 items-center justify-center rounded-full px-1.5 text-xs font-medium",
                            shouldParentBeActive
                              ? "bg-primary text-primary-foreground"
                              : "bg-muted text-muted-foreground"
                          )}
                        >
                          {item.badge}
                        </span>
                      )}

                      {/* Chevron for expandable items */}
                      {hasSubItems && (
                        <ChevronRight
                          className={cn(
                            "h-4 w-4 transition-all duration-200 flex-shrink-0",
                            isOpen && "rotate-90",
                            shouldParentBeActive
                              ? "text-primary"
                              : "text-muted-foreground"
                          )}
                        />
                      )}
                    </div>
                  </SidebarMenuButton>
                </CollapsibleTrigger>

                {hasSubItems && (
                  <CollapsibleContent className="transition-all duration-200 data-[state=closed]:animate-slide-up data-[state=open]:animate-slide-down">
                    <SidebarMenuSub className="ml-6 mt-1 space-y-1 border-l border-border/50 pl-4">
                      {item.items?.map((subItem) => {
                        const isSubItemActive = isPathActive(subItem.url);

                        return (
                          <SidebarMenuSubItem key={subItem.title}>
                            <SidebarMenuSubButton
                              asChild
                              className={cn(
                                "h-8 px-3 rounded-md transition-all duration-200",
                                "hover:bg-accent/50 focus:outline-none focus:ring-2 focus:ring-ring",
                                isSubItemActive && [
                                  "bg-gradient-to-r from-primary/10 to-transparent",
                                  "border-l-2 border-primary ml-[-1px]",
                                  "text-primary font-medium",
                                  "shadow-sm",
                                ],
                                !isSubItemActive &&
                                  "text-muted-foreground hover:text-foreground"
                              )}
                            >
                              <button
                                onClick={(event) =>
                                  handleNavigation(subItem.url, event)
                                }
                                className="w-full flex items-center gap-3 text-left"
                              >
                                {subItem.icon && (
                                  <subItem.icon
                                    className={cn(
                                      "h-3.5 w-3.5 flex-shrink-0",
                                      isSubItemActive
                                        ? "text-primary"
                                        : "text-muted-foreground"
                                    )}
                                  />
                                )}
                                <span className="truncate text-sm">
                                  {subItem.title}
                                </span>
                                {subItem.badge && (
                                  <span
                                    className={cn(
                                      "ml-auto flex h-4 min-w-4 items-center justify-center rounded-full px-1 text-xs font-medium",
                                      isSubItemActive
                                        ? "bg-primary text-primary-foreground"
                                        : "bg-muted text-muted-foreground"
                                    )}
                                  >
                                    {subItem.badge}
                                  </span>
                                )}
                              </button>
                            </SidebarMenuSubButton>
                          </SidebarMenuSubItem>
                        );
                      })}
                    </SidebarMenuSub>
                  </CollapsibleContent>
                )}
              </SidebarMenuItem>
            </Collapsible>
          );
        })}
      </SidebarMenu>
    </SidebarGroup>
  );
}
