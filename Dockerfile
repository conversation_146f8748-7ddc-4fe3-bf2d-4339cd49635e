# Stage 1: Build
FROM registryrb.litedoozy.com:5050/base/image-node:22-alpine AS builder
WORKDIR /usr/src/app

# Cài dependencies
COPY package.json package-lock.json* ./
RUN npm ci

# Copy toàn bộ source code
COPY . .

# Build Next.js (ra .next/standalone)
RUN npm run build

# Stage 2: Runtime (Node.js server)
FROM registryrb.litedoozy.com:5050/base/image-node:22-alpine AS runner
WORKDIR /usr/src/app

# Copy standalone build từ builder
COPY --from=builder /usr/src/app/.next/standalone ./
COPY --from=builder /usr/src/app/.next/static ./.next/static
RUN if [ -d /usr/src/app/public ]; then cp -r /usr/src/app/public ./public; fi

EXPOSE 3000

# Chạy Next.js standalone server
CMD ["node", "server.js"]
