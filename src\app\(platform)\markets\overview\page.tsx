"use client";
import { PageLayout } from "@/components/page-layout";
import { TabCategory } from "@/container/overview/tab-catgory";
import { Content, List, Tabs, TabsTrigger } from "@radix-ui/react-tabs";
import { Search, Star, TrendingDown, TrendingUp } from "lucide-react";
import { useMemo, useState } from "react";
interface CryptoCoin {
  id: string;
  symbol: string;
  name: string;
  price: number;
  change24h: number;
  volume24h: number;
  marketCap: number;
  logo: string;
}

type SortField = "price" | "change24h" | "volume24h" | "marketCap";
type SortOrder = "asc" | "desc";
const mockCryptoData = [
  {
    id: "bitcoin",
    symbol: "BTC",
    name: "Bitcoin",
    price: 116031,
    change24h: 0.23,
    volume24h: 51.55,
    marketCap: 2.31,
    logo: "🟠",
  },
  {
    id: "ethereum",
    symbol: "ETH",
    name: "Ethereum",
    price: 4715.11,
    change24h: 4.2,
    volume24h: 41.83,
    marketCap: 569.41,
    logo: "🔵",
  },
  {
    id: "xrp",
    symbol: "XRP",
    name: "X<PERSON>",
    price: 3.1163,
    change24h: 2.01,
    volume24h: 5.99,
    marketCap: 185.54,
    logo: "⚫",
  },
  {
    id: "tether",
    symbol: "USDT",
    name: "TetherUS",
    price: 1.0007,
    change24h: 0.02,
    volume24h: 136.87,
    marketCap: 169.63,
    logo: "🟢",
  },
  {
    id: "solana",
    symbol: "SOL",
    name: "Solana",
    price: 242.82,
    change24h: 4.35,
    volume24h: 12.17,
    marketCap: 132.36,
    logo: "🟣",
  },
  {
    id: "bnb",
    symbol: "BNB",
    name: "BNB",
    price: 928.51,
    change24h: 2.56,
    volume24h: 2.62,
    marketCap: 129.02,
    logo: "🟡",
  },
  {
    id: "usdc",
    symbol: "USDC",
    name: "USDC",
    price: 0.9993,
    change24h: -0.02,
    volume24h: 18.68,
    marketCap: 72.96,
    logo: "🔵",
  },
  {
    id: "dogecoin",
    symbol: "DOGE",
    name: "Dogecoin",
    price: 0.27997,
    change24h: 6.66,
    volume24h: 4.59,
    marketCap: 42.49,
    logo: "🟡",
  },
  {
    id: "tron",
    symbol: "TRX",
    name: "TRON",
    price: 0.352,
    change24h: 0.89,
    volume24h: 0.788,
    marketCap: 33.36,
    logo: "🔴",
  },
  {
    id: "cardano",
    symbol: "ADA",
    name: "Cardano",
    price: 1.15,
    change24h: -2.15,
    volume24h: 3.42,
    marketCap: 40.25,
    logo: "🔷",
  },
  {
    id: "avalanche",
    symbol: "AVAX",
    name: "Avalanche",
    price: 58.32,
    change24h: 1.85,
    volume24h: 1.89,
    marketCap: 24.18,
    logo: "🔺",
  },
  {
    id: "chainlink",
    symbol: "LINK",
    name: "Chainlink",
    price: 32.45,
    change24h: 3.21,
    volume24h: 2.15,
    marketCap: 20.67,
    logo: "🔗",
  },
];

const tabs = [
  "Favorites",
  "Cryptos",
  "Spot",
  "Futures",
  "Alpha",
  "New",
  "Zones",
];

export default function OverviewPage() {
  const breadcrumbs = [
    {
      label: "Overview",
      isCurrentPage: true,
      href: "/markets/overview",
    },
  ];
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [activeTab, setActiveTab] = useState<string>("Cryptos");
  const [activeCategory, setActiveCategory] = useState<string>("All");
  const [sortBy, setSortBy] = useState<SortField>("marketCap");
  const [sortOrder, setSortOrder] = useState<SortOrder>("desc");

  const filteredAndSortedData = useMemo((): CryptoCoin[] => {
    let filtered = mockCryptoData.filter(
      (coin: CryptoCoin) =>
        coin.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        coin.symbol.toLowerCase().includes(searchTerm.toLowerCase())
    );

    return filtered.sort((a: CryptoCoin, b: CryptoCoin) => {
      const aVal = a[sortBy];
      const bVal = b[sortBy];
      return sortOrder === "desc" ? bVal - aVal : aVal - bVal;
    });
  }, [searchTerm, sortBy, sortOrder]);

  const handleSort = (field: SortField): void => {
    if (sortBy === field) {
      setSortOrder(sortOrder === "desc" ? "asc" : "desc");
    } else {
      setSortBy(field);
      setSortOrder("desc");
    }
  };

  const formatPrice = (price: number): string => {
    if (price >= 1000) return `$${price.toLocaleString()}`;
    if (price >= 1) return `$${price.toFixed(2)}`;
    return `$${price.toFixed(4)}`;
  };

  const formatVolume = (volume: number): string => {
    return `$${volume.toFixed(2)}B`;
  };

  const formatMarketCap = (marketCap: number): string => {
    if (marketCap >= 1000) return `$${(marketCap / 1000).toFixed(2)}T`;
    return `$${marketCap.toFixed(2)}B`;
  };

  return (
    <PageLayout breadcrumbs={breadcrumbs}>
      <div>
        {/* Header Tabs */}
        {/* <div className="border-b border-border overflow-x-auto">
          <div className="flex items-center px-6 py-4 space-x-8">
            {tabs.map((tab: string) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`text-sm font-medium transition-colors relative ${
                  activeTab === tab
                    ? "text-primary"
                    : "text-muted-foreground hover:text-foreground"
                }`}
              >
                {tab}
                {tab === "New" && (
                  <small className="absolute -top-1 -right-8 bg-primary text-primary-foreground text-[10px] px-1 py-0.5 rounded self-start ml-1">
                    New
                  </small>
                )}
                {activeTab === tab && (
                  <div className="absolute bottom-[-16px] left-0 right-0 h-0.5 bg-primary"></div>
                )}
              </button>
            ))}
          </div>
        </div> */}

        {/* Categories */}
        {/* <TabCategory /> */}

        {/* Title and Search */}
        {/* <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-semibold text-foreground mb-2">
                Top Tokens by Market Capitalization
              </h1>
              <p className="text-sm text-muted-foreground">
                Get a comprehensive snapshot of all cryptocurrencies available
                on Binance. This page displays the latest prices, 24-hour
                trading volume, price changes, and market capitalizations for
                all cryptocurrencies on Binance...
                <button className="text-primary ml-2 hover:underline">
                  More
                </button>
              </p>
            </div>

            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <input
                type="text"
                placeholder="Search coin"
                value={searchTerm}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                  setSearchTerm(e.target.value)
                }
                className="bg-card border border-border rounded-lg pl-10 pr-4 py-2 text-sm text-foreground placeholder:text-muted-foreground focus:outline-none focus:border-primary focus:ring-1 focus:ring-primary w-64"
              />
            </div>
          </div>
        </div> */}

        {/* Table */}
        {/* <div className="px-6">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="text-left text-xs text-muted-foreground border-b border-border">
                  <th className="pb-3 font-normal">Name</th>
                  <th
                    className="pb-3 font-normal cursor-pointer hover:text-foreground transition-colors"
                    onClick={() => handleSort("price")}
                  >
                    <div className="flex items-center space-x-1">
                      <span>Price</span>
                      <div className="flex flex-col">
                        <TrendingUp className="w-2 h-2" />
                        <TrendingDown className="w-2 h-2" />
                      </div>
                    </div>
                  </th>
                  <th
                    className="pb-3 font-normal cursor-pointer hover:text-foreground transition-colors"
                    onClick={() => handleSort("change24h")}
                  >
                    <div className="flex items-center space-x-1">
                      <span>24h Change</span>
                      <div className="flex flex-col">
                        <TrendingUp className="w-2 h-2" />
                        <TrendingDown className="w-2 h-2" />
                      </div>
                    </div>
                  </th>
                  <th
                    className="pb-3 font-normal cursor-pointer hover:text-foreground transition-colors"
                    onClick={() => handleSort("volume24h")}
                  >
                    <div className="flex items-center space-x-1">
                      <span>24h Volume</span>
                      <div className="flex flex-col">
                        <TrendingUp className="w-2 h-2" />
                        <TrendingDown className="w-2 h-2" />
                      </div>
                    </div>
                  </th>
                  <th
                    className="pb-3 font-normal cursor-pointer hover:text-foreground transition-colors"
                    onClick={() => handleSort("marketCap")}
                  >
                    <div className="flex items-center space-x-1">
                      <span>Market Cap</span>
                      <div className="flex flex-col">
                        <TrendingUp className="w-2 h-2" />
                        <TrendingDown className="w-2 h-2" />
                      </div>
                    </div>
                  </th>
                  <th className="pb-3 font-normal">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredAndSortedData.map((coin: CryptoCoin) => (
                  <tr
                    key={coin.id}
                    className="border-b border-border hover:bg-muted/50 transition-colors"
                  >
                    <td className="py-4">
                      <div className="flex items-center space-x-3">
                        <button className="text-muted-foreground hover:text-primary transition-colors">
                          <Star className="w-4 h-4" />
                        </button>
                        <div className="flex items-center space-x-3">
                          <span className="text-2xl">{coin.logo}</span>
                          <div>
                            <div className="flex items-center space-x-2">
                              <span className="font-medium text-foreground">
                                {coin.symbol}
                              </span>
                              <span className="text-xs text-muted-foreground">
                                {coin.name}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="py-4">
                      <div className="text-foreground font-medium">
                        {formatPrice(coin.price)}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {formatPrice(coin.price)}
                      </div>
                    </td>
                    <td className="py-4">
                      <span
                        className={`font-medium ${
                          coin.change24h >= 0
                            ? "text-emerald-500"
                            : "text-red-500"
                        }`}
                      >
                        {coin.change24h >= 0 ? "+" : ""}
                        {coin.change24h.toFixed(2)}%
                      </span>
                    </td>
                    <td className="py-4 text-foreground font-medium">
                      {formatVolume(coin.volume24h)}
                    </td>
                    <td className="py-4 text-foreground font-medium">
                      {formatMarketCap(coin.marketCap)}
                    </td>
                    <td className="py-4">
                      <div className="flex items-center space-x-2">
                        <button className="p-2 hover:bg-muted rounded transition-colors">
                          <TrendingUp className="w-4 h-4 text-muted-foreground" />
                        </button>
                        <button className="p-2 hover:bg-muted rounded transition-colors">
                          <TrendingDown className="w-4 h-4 text-muted-foreground" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div> */}
      </div>
    </PageLayout>
  );
}
